﻿@page "/worklist"
@inject WorklistDataService Service
@attribute [Authorize]

@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center;justify-content: space-between">
    <MudText Typo="Typo.h5">Approval list</MudText>
    <SfButton OnClick="NavigateToMyDelegation" CssClass="e-primary">Delegation</SfButton>
</div>
<MyWorklist />
@code {
    private void NavigateToMyDelegation()
    {
        NavMgr.NavigateTo("/worklist/my-delegations");
    }
}

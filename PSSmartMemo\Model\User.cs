﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class User
{
    public int Id { get; set; }

    public string Code { get; set; }

    public string Name { get; set; }

    public string Email { get; set; }

    public string MobileNumber { get; set; }

    public string Password { get; set; }

    public string Pin { get; set; }

    public string Note { get; set; }

    public bool IsAdmin { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? ModifiedBy { get; set; }

    public string UserId { get; set; }

    public string EmployeeCode { get; set; }

    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
}
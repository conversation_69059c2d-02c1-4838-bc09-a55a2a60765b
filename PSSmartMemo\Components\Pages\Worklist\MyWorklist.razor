﻿@inject WorklistDataService Service
@inject DelegationDataService DelegationService
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Navigations
@*<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>*@
@* <div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Approval list</MudText>

</div> *@


<div class="row">
    <div class="col-md">
        <SfTab Height="calc(100vh - 150px)">
            <TabItems>
                <Syncfusion.Blazor.Navigations.TabItem>
                    <ChildContent>
<TabHeader Text="@($"My Worklist ({worklist.Count})")"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <SfGrid DataSource="worklist" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                            <GridEvents TValue="MemoDto" RowDataBound="OnRowDataBind"></GridEvents>
                            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn HeaderText="Code" AutoFit="true">
                                    <Template Context="kk">
                                        @{
                                            if (kk is MemoDto mm)
                                            {
                                                <a href="@($"/worklist/viewmemo/{mm.MemoApprovalLogId}")"
                                                   target="memo-@(mm.MemoId)"
                                                   id="@($"mm-{mm.MemoId}")"
                                                   class="mud-link"
                                                   style="color: #0d6efd; text-decoration: none; font-weight: bold;;">@mm.MemoCode</a>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Memo" Field="@nameof(MemoDto.MemoTitle)" AutoFit="true">

                                </GridColumn>
                                <GridColumn HeaderText="Type" Field="@nameof(MemoDto.MemoTyeShort)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated By" Field="@nameof(MemoDto.MemoCreatedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated Date" Field="@nameof(MemoDto.MemoCreatedDate)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Pedning Since" Field="@nameof(MemoDto.PendingSince)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Forward By" Field="@nameof(MemoDto.ForwardedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Last Action" AutoFit="true" Field="LastAction">
                                    <Template Context="context">
                                        @{
                                            if (context is MemoDto memo)
                                            {
                                                <div style="display:flex;gap:10px;">
                                                <span>@memo.LastAction</span>
                                                @if (memo.LastAction == "Object")
                                                {

                                                    <MudLink Href="@($"/memos/{memo.MemoId}/edit")" Style="font-size: 12px;">
                                                        Edit Memo
                                                    </MudLink>
                                                }
                                                </div>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </ContentTemplate>
                </Syncfusion.Blazor.Navigations.TabItem>
                @if(delegations.Any())
                {
<Syncfusion.Blazor.Navigations.TabItem>
                    <ChildContent>
                        <TabHeader Text="Delegations Assigned to Me"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <SfGrid DataSource="delegations" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn HeaderText="From User" Field="@nameof(DelegationDto.FromUserName)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Type" Field="@nameof(DelegationDto.Type)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Date From" Field="@nameof(DelegationDto.DateFrom)" AutoFit="true" Format="dd/MM/yyyy"></GridColumn>
                                <GridColumn HeaderText="Date To" Field="@nameof(DelegationDto.DateTo)" AutoFit="true" Format="dd/MM/yyyy"></GridColumn>
                                <GridColumn HeaderText="Comments" Field="@nameof(DelegationDto.Comments)" AutoFit="true"></GridColumn>
                                
                            </GridColumns>
                        </SfGrid>
                    </ContentTemplate>
                </Syncfusion.Blazor.Navigations.TabItem>

                }
                @if(delegatedWorklist.Any())
                {
                                <Syncfusion.Blazor.Navigations.TabItem>
                    <ChildContent>
                        <TabHeader Text="@($"Delegated Worklist ({delegatedWorklist.Count})")"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <SfGrid DataSource="delegatedWorklist" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                            <GridEvents TValue="MemoDto" RowDataBound="OnRowDataBind"></GridEvents>
                            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn HeaderText="Code" AutoFit="true">
                                    <Template Context="kk">
                                        @{
                                            if (kk is MemoDto mm)
                                            {
                                                <a href="@($"/worklist/viewmemo/{mm.MemoApprovalLogId}")"
                                                   target="memo-@(mm.MemoId)"
                                                   id="@($"mm-{mm.MemoId}")"
                                                   class="mud-link"
                                                   style="color: #0d6efd; text-decoration: none; font-weight: bold;;">@mm.MemoCode</a>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Memo" Field="@nameof(MemoDto.MemoTitle)" AutoFit="true">

                                </GridColumn>
                                <GridColumn HeaderText="Type" Field="@nameof(MemoDto.MemoTyeShort)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated By" Field="@nameof(MemoDto.MemoCreatedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated Date" Field="@nameof(MemoDto.MemoCreatedDate)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Pedning Since" Field="@nameof(MemoDto.PendingSince)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Forward By" Field="@nameof(MemoDto.ForwardedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Last Action" AutoFit="true" Field="LastAction">
                                    <Template Context="context">
                                        @{
                                            if (context is MemoDto memo)
                                            {
                                                <div style="display:flex;gap:10px;">
                                                <span>@memo.LastAction</span>
                                                @if (memo.LastAction == "Object")
                                                {

                                                    <MudLink Href="@($"/memos/{memo.MemoId}/edit")" Style="font-size: 12px;">
                                                        Edit Memo
                                                    </MudLink>
                                                }
                                                </div>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </ContentTemplate>
                </Syncfusion.Blazor.Navigations.TabItem>
                }
            </TabItems>
        </SfTab>
    </div>
</div>
@code {
    // authorization
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<MemoDto> worklist { get; set; } = new();
    private List<DelegationDto> delegations { get; set; } = new();
    private List<MemoDto> delegatedWorklist { get; set; } = new();


    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        worklist = await Service.GetMyWorkList(userId);
        delegations = await DelegationService.GetDelegationsAssignedToUser(userId);

        // Get delegated worklist
        var delegatedFromUserIds = await DelegationService.GetDelegatedFromUserIds(userId);
        delegatedWorklist = await Service.GetDelegatedWorkList(delegatedFromUserIds);
    }

    public void ViewMemo(MemoDto memo)
    {
        NavMgr.NavigateTo($"/worklist/viewmemo/{memo.MemoApprovalLogId}");
    }

    private void OnRowDataBind(RowDataBoundEventArgs<MemoDto> obj)
    {
        if (obj.Data.LastAction == "Approved")
        {
            obj.Row.AddStyle(["background-color:#B7FFB7 "]);
        }
        else if (obj.Data.LastAction == "Object")
        {
            obj.Row.AddStyle(["background-color:#FF6699 "]);

        }
        else if (obj.Data.LastAction == "Reply")
        {
            obj.Row.AddStyle(["background-color:#ffd7c4 "]);
        }
    }

}

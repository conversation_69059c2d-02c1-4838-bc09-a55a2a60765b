﻿@page "/worklist/my-delegations"
@inject DelegationDataService Service
@inject AdminDataService UserService
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@attribute [Authorize]
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Delegations" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center;justify-content:space-between">
    <MudText Typo="Typo.h5">My Delegations</MudText>
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@MudBlazor.Icons.Material.Filled.Add">Create</MudButton>
</div>

<SfGrid DataSource="@delegations" AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        @*<GridColumn Field="FromUserId" HeaderText="From User Id" AutoFit="true"></GridColumn>*@
       @* <GridColumn Field="@nameof(DelegationDto.FromUserName)" HeaderText="From User" AutoFit="true"></GridColumn>*@
        <GridColumn Field="ToUserId" HeaderText="Delegate To User Id" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(DelegationDto.ToUserName)" HeaderText="Delete To" AutoFit="true"></GridColumn>
        <GridColumn Field="Type" HeaderText="Type" AutoFit="true"></GridColumn>
        <GridColumn Field="DateFrom" HeaderText="From Date" Format="dd/MM/yyyy" AutoFit="true"></GridColumn>
        <GridColumn Field="DateTo" HeaderText="To Date" Format="dd/MM/yyyy" AutoFit="true"></GridColumn>
        <GridColumn Field="Status" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" AutoFit="true" TextAlign="TextAlign.Center">
            <Template>
                <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(args => EditDelegation((context as DelegationDto)))"></SfButton>
                <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(args => DeleteDelegation((context as DelegationDto)))"></SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dialog" Width="600px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@dialogTitle</Header>
        <Content>
            <EditForm Model="@delegation" OnValidSubmit="SaveDelegation">
                <DataAnnotationsValidator />
                <ValidationSummary />
                @*<div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList TValue="string" TItem="UserDTO" DataSource="@users"
                                        @bind-Value="delegation.FromUserId"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Placeholder="Select From User"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Name" Value="UserId"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => delegation.FromUserId)" />
                    </div>
                </div>*@
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList TValue="string" TItem="UserDTO" DataSource="@users"
                                        @bind-Value="delegation.ToUserId"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Placeholder="Select To User"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Name" Value="UserId"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => delegation.ToUserId)" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList TValue="string" TItem="DelegationType" DataSource="@delegationTypes"
                                        @bind-Value="delegation.Type"
                                        Placeholder="Select Type"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Name" Value="Value"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => delegation.Type)" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Comments" @bind-Value="delegation.Comments"
                                   FloatLabelType="FloatLabelType.Always"
                                   Multiline="true"></SfTextBox>
                    </div>

                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDatePicker @bind-Value="delegation.DateFrom" Placeholder="From Date" FloatLabelType="FloatLabelType.Always"
                                      Format="d-MMM-yyyy"></SfDatePicker>
                        <ValidationMessage For="@(() => delegation.DateFrom)" />
                    </div>
                    <div class="col-md">
                        <SfDatePicker @bind-Value="delegation.DateTo" Placeholder="To Date" FloatLabelType="FloatLabelType.Always"
                                      Format="d-MMM-yyyy"></SfDatePicker>
                        <ValidationMessage For="@(() => delegation.DateTo)" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <label>Active</label>
                        <SfSwitch @bind-Checked="delegation.IsActive" OnLabel="Yes" OffLabel="No"></SfSwitch>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<DelegationDto> delegations = new();
    private List<UserDTO> users = new();
    private DelegationDto delegation = new();
    private string dialogTitle = "Add Delegation";
    private SfGrid<DelegationDto> grid;
    private SfDialog dialog;

    private List<DelegationType> delegationTypes = new()
{
        new DelegationType { Name = "Out of Office", Value = "Out Of Office" },
        new DelegationType { Name = "Absent", Value = "Absent" },
        new DelegationType { Name = "Business Trip", Value = "Business Trip" },
        new DelegationType { Name = "Training", Value = "Training" },
        new DelegationType { Name = "Other", Value = "Other" }
    };

    protected override async Task OnInitializedAsync()
    {

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        delegations = await Service.GetAll(userId);
        users = await UserService.GetAllUsers();
        users = users.Where(c => c.UserId.ToLower() != userId.ToLower()).ToList();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task OpenCreateForm()
    {
        delegation = new DelegationDto();
        delegation.FromUserId = userId;
        delegation.IsActive = true;
        dialogTitle = "Add Delegation";
        await dialog.ShowAsync();
    }

    private async Task EditDelegation(DelegationDto delegationDto)
    {
        delegation = delegationDto;
        delegation.FromUserId = userId;
        dialogTitle = "Edit Delegation";
        await dialog.ShowAsync();
    }

    private async Task SaveDelegation()
    {
        delegation.FromUserId = userId;
        var result = await Service.Save(delegation, userId);
        if (result.Item2 == "OK")
        {
            delegations = await Service.GetAll(userId);
            await dialog.HideAsync();
        }
        else
        {
            // Handle error
        }
    }

    private async Task DeleteDelegation(DelegationDto delegationDto)
    {
        var result = await Service.DeleteById(delegationDto.Id, userId);
        if (result == "OK")
        {
            delegations = await Service.GetAll(userId);
        }
        else
        {
            // Handle error
        }
    }

    public class DelegationType
    {
        public string Name { get; set; }
        public string Value { get; set; }
    }
} 
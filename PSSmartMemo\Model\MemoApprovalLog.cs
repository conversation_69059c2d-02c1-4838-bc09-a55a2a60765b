﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoApprovalLog
{
    public int MemoApprovalLogId { get; set; }

    public int MemoId { get; set; }

    public int FromApproverId { get; set; }

    public int? ToApproverId { get; set; }

    public byte? ApprovalActionsId { get; set; }

    public string Comments { get; set; }

    public DateTime ActionDate { get; set; }

    public string ToApproverUserId { get; set; }

    public int? ReplyLogId { get; set; }

    public bool IsForwarded { get; set; }

    public int? PrevReplyLogId { get; set; }

    public string DraftComments { get; set; }

    public byte? DraftActionId { get; set; }

    public int? DraftToUserId { get; set; }

    public string DelegatedUserId { get; set; }

    public virtual ApprovalAction ApprovalActions { get; set; }

    public virtual MemoApprover FromApprover { get; set; }

    public virtual ICollection<MemoApprovalLog> InversePrevReplyLog { get; set; } = new List<MemoApprovalLog>();

    public virtual ICollection<MemoApprovalLog> InverseReplyLog { get; set; } = new List<MemoApprovalLog>();

    public virtual Memo Memo { get; set; }

    public virtual MemoApprovalLog PrevReplyLog { get; set; }

    public virtual MemoApprovalLog ReplyLog { get; set; }

    public virtual MemoApprover ToApprover { get; set; }
}
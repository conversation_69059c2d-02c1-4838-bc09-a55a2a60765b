﻿using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class AttachmentTypeDto
{
    public int Id { get; set; }
    [Required]
    public string Title { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "Inactive";
    public string? Description { get; set; }
    public int? MemoTypeId { get; set; }
    public string? MemoType { get; set; }
}

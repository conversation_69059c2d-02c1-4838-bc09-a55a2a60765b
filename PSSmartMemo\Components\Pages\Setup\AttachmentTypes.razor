﻿@page "/setup/attachment-types"
@inject AttachmentTypeDataService Service
@inject MemoTypeDataService MemoTypeService
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Buttons

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Attachment Types" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<SfToast @ref="toastObj"></SfToast>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Attachment Types</MudText>
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@MudBlazor.Icons.Material.Filled.Add">Create</MudButton>
</div>

<SfGrid DataSource="@attachmentTypes" AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="Title" HeaderText="Title" AutoFit="true"></GridColumn>
        <GridColumn Field="MemoType" HeaderText="Memo Type" AutoFit="true"></GridColumn>
        <GridColumn Field="Description" HeaderText="Description" AutoFit="true"></GridColumn>
        <GridColumn Field="Status" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" AutoFit="true" TextAlign="TextAlign.Center">
            <Template>
                <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(args => EditAttachmentType((context as AttachmentTypeDto)))"></SfButton>
                <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(args => DeleteAttachmentType((context as AttachmentTypeDto)))"></SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dialog" Width="500px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@dialogTitle</Header>
        <Content>
            <EditForm Model="@attachmentType" OnValidSubmit="SaveAttachmentType">
                <DataAnnotationsValidator />
                
                
                <div class="row mb-3">
                    <div class="col-md">
                        <SfDropDownList TValue="int?" TItem="MemoTypeDto"
                                      @bind-Value="attachmentType.MemoTypeId"
                                      DataSource="@memoTypes"
                                      Placeholder="Memo Type"
                                      FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => attachmentType.MemoTypeId)" />
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" @bind-Value="attachmentType.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => attachmentType.Title)" />
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Multiline="true" Placeholder="Description" @bind-Value="attachmentType.Description" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <div style="display:flex; flex-direction: column;">
                            <label>Active</label>
                            <SfSwitch @bind-Checked="attachmentType.IsActive" OnLabel="Yes" OffLabel="No"></SfSwitch>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md">
                        <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<AttachmentTypeDto> attachmentTypes = new();
    private List<MemoTypeDto> memoTypes = new();
    private AttachmentTypeDto attachmentType = new();
    private string dialogTitle = "Add Attachment Type";
    private SfGrid<AttachmentTypeDto> grid;
    private SfDialog dialog;
    private int? selectedMemoTypeId;
    private SfToast? toastObj;
    protected override async Task OnInitializedAsync()
    {
        memoTypes = await MemoTypeService.GetAll();
        await LoadAttachmentTypes();
        
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task LoadAttachmentTypes()
    {
        attachmentTypes = await Service.GetAll();
        if (selectedMemoTypeId.HasValue)
        {
            attachmentTypes = attachmentTypes.Where(x => x.MemoTypeId == selectedMemoTypeId).ToList();
        }
    }


    private async Task OpenCreateForm()
    {
        attachmentType = new AttachmentTypeDto 
        { 
            IsActive = true,
            MemoTypeId = selectedMemoTypeId
        };
        dialogTitle = "Add Attachment Type";
        await dialog.ShowAsync();
    }

    private async Task EditAttachmentType(AttachmentTypeDto dto)
    {
        attachmentType = dto;
        dialogTitle = "Edit Attachment Type";
        await dialog.ShowAsync();
    }

    private async Task SaveAttachmentType()
    {
        var result = await Service.Save(attachmentType, userId);
        if (result.Item2 == "OK")
        {
            await LoadAttachmentTypes();
            await dialog.HideAsync();
        }
        else
        {
            // Handle error - you might want to add a toast notification here
            var mm = new ToastModel() { Content = result.Item2, Title = "Error", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
            await toastObj?.ShowAsync(mm);
        }
    }

    private async Task DeleteAttachmentType(AttachmentTypeDto dto)
    {
        var result = await Service.DeleteById(dto.Id, userId);
        if (result == "OK")
        {
            await LoadAttachmentTypes();
        }
        else
        {
            // Handle error - you might want to add a toast notification here
        }
    }
}
